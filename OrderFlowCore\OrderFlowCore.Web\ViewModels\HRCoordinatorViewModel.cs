using Microsoft.AspNetCore.Mvc.Rendering;
using OrderFlowCore.Application.DTOs;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace OrderFlowCore.Web.ViewModels
{
    public class HRCoordinatorViewModel
    {
        public HRCoordinatorViewModel()
        {
            OrderNumbers = new List<SelectListItem>();
            RestorableOrders = new List<SelectListItem>();
            SupervisorCheckboxes = new List<SupervisorCheckboxViewModel>();
            SupervisorRejections = new List<SupervisorRejectionDto>();
        }

        // Order Selection
        public List<SelectListItem> OrderNumbers { get; set; }
        public int SelectedOrderId { get; set; }

        // Order Details
        public string OrderNumber { get; set; }
        public string OrderDate { get; set; }
        public string OrderStatus { get; set; }
        public string OrderType { get; set; }
        public string EmployeeName { get; set; }
        public string Department { get; set; }
        public string JobTitle { get; set; }
        public string EmployeeNumber { get; set; }
        public string CivilRegistry { get; set; }
        public string Nationality { get; set; }
        public string MobileNumber { get; set; }
        public string EmploymentType { get; set; }
        public string Qualification { get; set; }
        public string Notes { get; set; }
        public string ManagerApproval { get; set; }
        public string SupervisorApproval { get; set; }
        public string CoordinatorApproval { get; set; }
        public string CancellationReason { get; set; }
        public string CoordinatorDetails { get; set; }
        public string HRManagerApproval { get; set; }

        // Supervisor Permissions
        public string MedicalServicesPermission { get; set; }
        public string HRPlanningPermission { get; set; }
        public string ITPermission { get; set; }
        public string AttendanceControlPermission { get; set; }
        public string MedicalRecordsPermission { get; set; }
        public string PayrollPermission { get; set; }
        public string LegalCompliancePermission { get; set; }
        public string HRServicesPermission { get; set; }
        public string HousingPermission { get; set; }
        public string FilesSectionPermission { get; set; }
        public string OutpatientPermission { get; set; }
        public string SocialInsurancePermission { get; set; }
        public string InventoryControlPermission { get; set; }
        public string SelfResourcesPermission { get; set; }
        public string NursingPermission { get; set; }
        public string EmployeeServicesPermission { get; set; }

        // File URLs
        public string File1Url { get; set; }
        public string File2Url { get; set; }
        public string File3Url { get; set; }
        public string File4Url { get; set; }

        // Auto-Routing Information
        public AutoRoutingInfoDto AutoRoutingInfo { get; set; }

        // Supervisor Rejections
        public List<SupervisorRejectionDto> SupervisorRejections { get; set; }

        // Supervisor Selection
        public List<SupervisorCheckboxViewModel> SupervisorCheckboxes { get; set; }
        public List<string> SelectedSupervisors { get; set; } = new List<string>();

        // Action Fields
        [Required(ErrorMessage = "يرجى كتابة التفاصيل/الرقم")]
        public string Details { get; set; }

        public string ActionRequired { get; set; }

        [Required(ErrorMessage = "يرجى إدخال سبب الإعادة")]
        public string ReturnReason { get; set; }

        [Required(ErrorMessage = "يرجى إدخال سبب الإلغاء")]
        public string RejectReason { get; set; }

        // Restore Section
        public List<SelectListItem> RestorableOrders { get; set; }
        public int SelectedRestoreOrderId { get; set; }
        public string SearchTerm { get; set; }
        public string FilterType { get; set; } = "today";
        public bool ShowRestoreSection { get; set; }

        // Restore Order Details
        public string RestoreCurrentStatus { get; set; }
        public string TransferDate { get; set; }
        public string AssignedSupervisors { get; set; }
        public string RestoreNotes { get; set; }

        // UI State
        public bool ShowOrderDetails { get; set; }
        public bool ShowRestoreDetails { get; set; }
        public string AutoPathButtonClass { get; set; } = "auto-path-btn disabled-path";
        public bool AutoPathEnabled { get; set; }
        public string AutoPathMessage { get; set; }

        public static HRCoordinatorViewModel FromOrderDetails(OrderDetailsDto orderDetails)
        {
            var viewModel = new HRCoordinatorViewModel
            {
                OrderNumber = orderDetails.Id.ToString(),
                OrderDate = orderDetails.CreatedAt.ToString("yyyy-MM-dd"),
                OrderStatus = orderDetails.OrderStatus.ToString(),
                OrderType = orderDetails.OrderType,
                EmployeeName = orderDetails.EmployeeName,
                Department = orderDetails.Department,
                JobTitle = orderDetails.JobTitle,
                EmployeeNumber = orderDetails.EmployeeNumber,
                CivilRegistry = orderDetails.CivilRecord,
                Nationality = orderDetails.Nationality,
                MobileNumber = orderDetails.MobileNumber,
                EmploymentType = orderDetails.EmploymentType,
                Qualification = orderDetails.Qualification,
                Notes = orderDetails.SupervisorNotes,
                ManagerApproval = orderDetails.HumanResourcesManager,
                SupervisorApproval = orderDetails.SupervisorNotes,
                CoordinatorApproval = orderDetails.ConfirmedByCoordinator,
                CancellationReason = orderDetails.ReasonForCancellation,
                CoordinatorDetails = orderDetails.CoordinatorDetails,
                HRManagerApproval = orderDetails.ConfirmedByAssistantManager,
                File1Url = orderDetails.File1Url,
                File2Url = orderDetails.File2Url,
                File3Url = orderDetails.File3Url,
                File4Url = orderDetails.File4Url,
                ShowOrderDetails = true
            };

            // Initialize supervisor checkboxes
            viewModel.SupervisorCheckboxes = new List<SupervisorCheckboxViewModel>
            {
                new SupervisorCheckboxViewModel { Id = "chk1", Text = "خدمات الموظفين", IsChecked = false },
                new SupervisorCheckboxViewModel { Id = "chk2", Text = "إدارة تخطيط الموارد البشرية", IsChecked = false },
                new SupervisorCheckboxViewModel { Id = "chk3", Text = "إدارة تقنية المعلومات", IsChecked = false },
                new SupervisorCheckboxViewModel { Id = "chk4", Text = "مراقبة الدوام", IsChecked = false },
                new SupervisorCheckboxViewModel { Id = "chk5", Text = "السجلات الطبية", IsChecked = false },
                new SupervisorCheckboxViewModel { Id = "chk6", Text = "إدارة الرواتب والاستحقاقات", IsChecked = false },
                new SupervisorCheckboxViewModel { Id = "chk7", Text = "إدارة القانونية والالتزام", IsChecked = false },
                new SupervisorCheckboxViewModel { Id = "chk8", Text = "خدمات الموارد البشرية", IsChecked = false },
                new SupervisorCheckboxViewModel { Id = "chk9", Text = "إدارة الإسكان", IsChecked = false },
                new SupervisorCheckboxViewModel { Id = "chk10", Text = "قسم الملفات", IsChecked = false },
                new SupervisorCheckboxViewModel { Id = "chk11", Text = "العيادات الخارجية", IsChecked = false },
                new SupervisorCheckboxViewModel { Id = "chk12", Text = "التأمينات الاجتماعية", IsChecked = false },
                new SupervisorCheckboxViewModel { Id = "chk13", Text = "وحدة مراقبة المخزون", IsChecked = false },
                new SupervisorCheckboxViewModel { Id = "chk14", Text = "إدارة تنمية الإيرادات", IsChecked = false },
                new SupervisorCheckboxViewModel { Id = "chk15", Text = "إدارة الأمن و السلامة", IsChecked = false },
                new SupervisorCheckboxViewModel { Id = "chk16", Text = "الطب الاتصالي", IsChecked = false }
            };

            return viewModel;
        }
    }

    public class SupervisorCheckboxViewModel
    {
        public string Id { get; set; }
        public string Text { get; set; }
        public bool IsChecked { get; set; }
    }
}
