﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Paths.aspx.cs" Inherits="abozyad.WebForm8" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    <!-- إضافة ScriptManager في بداية المحتوى -->







    <!-- بداية محتوى الصفحة -->
    <div class="container">


        <!-- زر التبويب -->
        <div class="routing-tabs">




            <asp:Button ID="btnManualTab" runat="server" Text="المسارات اليدوية" OnClick="btnManualTab_Click" CssClass="tab-btn" />
            <asp:Button ID="btnAutoTab" runat="server" Text="التوجيه التلقائي" OnClick="btnAutoTab_Click" CssClass="tab-btn" />
            <asp:Button ID="btnDirectTab" runat="server" Text="المسار السريع" OnClick="btnDirectTab_Click" CssClass="tab-btn" />
            <asp:Button ID="btnSettingsTab" runat="server" Text="الإعدادات" OnClick="btnSettingsTab_Click" CssClass="tab-btn" />
        </div>
    </div>

    <!--   لوحة الإعدادات -->
<asp:Panel ID="pnlSettings" runat="server" CssClass="tab-panel" Visible="false">
    <div class="row">
        <div class="col-md-12">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 style="color: #007bff;">⚙️ إعدادات النظام</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- إعدادات المسارات -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>الحد الأقصى للمسارات النشطة:</label>
                                <asp:TextBox ID="txtMaxActivePaths" runat="server" CssClass="form-control" TextMode="Number" />
                            </div>
                        </div>
                        
                        <!-- إعدادات المشرفين -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>الحد الأقصى للمشرفين في المسار:</label>
                                <asp:TextBox ID="txtMaxSupervisors" runat="server" CssClass="form-control" TextMode="Number" />
                            </div>
                        </div>
                        
                        <!-- إعدادات عامة -->
                        <div class="col-md-12 mt-3">
                            <div class="form-group">
                                <asp:CheckBox ID="chkAutoDeactivate" runat="server" Text="تعطيل المسارات القديمة تلقائياً" CssClass="custom-checkbox" />
                            </div>
                            <div class="form-group">
                                <asp:CheckBox ID="chkRequireNotes" runat="server" Text="إلزامية إدخال الملاحظات" CssClass="custom-checkbox" />
                            </div>
                        </div>
                    </div>
                    
                    <!-- زر حفظ الإعدادات -->
                    <div class="mt-4">
                        <asp:Button ID="btnSaveSettings" runat="server" Text="حفظ الإعدادات" 
                            CssClass="btn btn-primary" OnClick="btnSaveSettings_Click" />
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- أزرار الاستيراد والتصدير -->
<div class="col-md-12 mt-4">
    <div class="card">
        <div class="card-header">
            <h5 style="color: #007bff;">📋 إدارة بيانات الموظفين</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-12">
                    <div class="import-export-section">
                        <label class="custom-upload-button">
                            <asp:FileUpload ID="fileUploadEmployees" runat="server" accept=".csv" 
                                style="display: none;" onchange="updateFileName()" />
                            استيراد CSV
                        </label>
                        <span id="fileName" class="upload-status-text">لم يتم اختيار ملف</span>
                        <asp:Button ID="btnImportEmployees" runat="server" Text="تحميل الملف" 
                            OnClick="btnImportEmployees_Click" CssClass="btn btn-primary mx-2" />
                        <asp:Button ID="btnExportEmployees" runat="server" Text="تصدير CSV" 
                            OnClick="btnExportEmployees_Click" CssClass="btn btn-success" />
                        <asp:Button ID="btnDeleteEmployees" runat="server" Text="حذف جميع البيانات" CssClass="danger-button" OnClick="btnDeleteEmployees_Click" OnClientClick="return confirm('هل أنت متأكد من أنك تريد حذف جميع البيانات؟');" />
    <button type="button" class="action-button" onclick="updateEmployeeCount()">تحديث الإحصائيات</button>
                    </div>
                </div>
            </div>
            <div id="employeeCountSection">
                <p>إجمالي عدد الموظفين: <span id="employeeCount">--</span></p>
            </div>
        </div>
    </div>
</div>
</asp:Panel>


    <!-- 1. المسار السريع -->
    <asp:UpdatePanel ID="UpdatePanel2" runat="server">
        <ContentTemplate>




<asp:Panel ID="pnlDirectRouting" runat="server" CssClass="tab-panel" Visible="false">
    <div class="row">
        <!-- إضافة مسار سريع جديد -->
        <div class="col-md-12">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 style="color: #007bff;">✨ إضافة مسار سريع جديد</h5>
                </div>
                <div class="card-body">
                                <asp:UpdatePanel ID="UpdatePanel1" runat="server">
                                    <ContentTemplate>
<div class="row">
    <!-- نوع الطلب -->
    <div class="col-md-4 form-group">
        <label>نوع الطلب:</label>
        <asp:DropDownList ID="ddlDirectRequestType" runat="server" CssClass="form-control"></asp:DropDownList>
    </div>

    <!-- الوظيفة -->
    <div class="col-md-4 form-group">
        <label>الوظيفة:</label>
        <asp:DropDownList ID="ddlDirectRequestJob" runat="server" CssClass="form-control">
            <asp:ListItem Text="الكل" Value="ALL" />
        </asp:DropDownList>
    </div>

    <!-- الجنسية -->
    <div class="col-md-4 form-group">
        <label>الجنسية:</label>
        <asp:DropDownList ID="ddlDirectNationality" runat="server" CssClass="form-control">
            <asp:ListItem Text="" Value="" />
            <asp:ListItem Text="سعودي - Saudi" Value="سعودي - Saudi" />
            <asp:ListItem Text="غير سعودي - Non-Saudi" Value="غير سعودي - Non-Saudi" />
        </asp:DropDownList>
    </div>
</div>


                                        <!-- المشرفين -->
                                        <div class="form-group">
                                            <label>تحديد المشرفين:</label>
                                            <div class="supervisors-list">
                                                <asp:CheckBoxList ID="cblDirectSupervisors" runat="server"
                                                    CssClass="checkbox-list"
                                                    RepeatDirection="Vertical">
                                                </asp:CheckBoxList>
                                            </div>
                                        </div>

                                        <!-- ملاحظات -->
                                        <div class="form-group">
                                            <label>ملاحظات:</label>
                                            <asp:TextBox ID="txtDirectNotes" runat="server"
                                                TextMode="MultiLine"
                                                CssClass="form-control"
                                                Rows="3">
                                            </asp:TextBox>
                                        </div>

                                        <!-- تفعيل المسار -->
                                        <div class="form-group">
                                            <asp:CheckBox ID="chkDirectActive" runat="server"
                                                Text="تفعيل المسار"
                                                Checked="true"
                                                CssClass="custom-checkbox" />
                                        </div>

                                        <!-- زر الحفظ -->
                                        <asp:Button ID="btnSaveDirectRoute" runat="server"
                                            Text="حفظ المسار"
                                            CssClass="btn btn-primary"
                                            OnClick="btnSaveDirectRoute_Click" />

                                        <asp:Label ID="LabelMessage" runat="server" ForeColor="Green" Visible="false" />
                                        <asp:Label ID="LabelError" runat="server" ForeColor="Red" Visible="false" />


                                    </ContentTemplate>
                                </asp:UpdatePanel>
                            </div>
                        </div>
                    </div>

                    <!-- عرض المسارات الحالية -->
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h5  style="color: #007bff;">📋 المسارات السريعة النشطة</h5>
                                <!-- إضافة العداد هنا -->
                                <asp:Label ID="lblActiveCount" runat="server"
                                    ForeColor="Green" >عدد المسارات النشطة</asp:Label>
                            </div>
                            <div class="card-body">
                                <asp:GridView ID="gvDirectRoutes" runat="server"
                                    CssClass="table table-bordered table-hover"
                                    AutoGenerateColumns="false"
                                    OnRowCommand="gvDirectRoutes_RowCommand1">
                                    <Columns>
                                        <asp:BoundField DataField="نوع_الطلب" HeaderText="نوع الطلب" />
                                        <asp:BoundField DataField="الجنسية" HeaderText="الجنسية" />
                                        <asp:BoundField DataField="الوظيفة" HeaderText="الوظيفة" />
                                        <asp:BoundField DataField="تاريخ_الإنشاء" HeaderText="تاريخ الإنشاء" DataFormatString="{0:dd/MM/yyyy}" />
                                        <asp:TemplateField HeaderText="حالة المسار">
                                            <ItemTemplate>
                                                <asp:Label runat="server"
                                                    CssClass='<%# Eval("حالة_المسار").ToString() == "True" ? "badge badge-success" : "badge badge-danger" %>'
                                                    Text='<%# Eval("حالة_المسار").ToString() == "True" ? "نشط" : "غير نشط" %>'>
                                                </asp:Label>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        <asp:TemplateField HeaderText="العمليات">
                                            <ItemTemplate>
                                                <asp:LinkButton ID="btnEdit" runat="server"
                                                    CommandName="EditRoute"
                                                    CommandArgument='<%# Eval("ID") %>'
                                                    CssClass="btn btn-sm btn-info">
                                        <i class="fas fa-edit"></i> تعديل
                                                </asp:LinkButton>
                                                <asp:LinkButton ID="btnToggle" runat="server"
                                                    CommandName="ToggleStatus"
                                                    CommandArgument='<%# Eval("ID") %>'
                                                    CssClass='<%# Eval("حالة_المسار").ToString() == "True" ? "btn btn-sm btn-warning" : "btn btn-sm btn-success" %>'>
                                        <i class="fas fa-power-off"></i> 
                                        <%# Eval("حالة_المسار").ToString() == "True" ? "تعطيل" : "تفعيل" %>

                                                        </asp:LinkButton>

        <asp:LinkButton ID="btnAutoDelete" runat="server"
            CommandName="DeleteRoute"
            CommandArgument='<%# Eval("ID") %>'
            CssClass="btn btn-sm btn-danger"
            OnClientClick="return confirm('هل أنت متأكد من حذف هذا المسار؟');">
            <i class="fas fa-trash"></i> حذف
        </asp:LinkButton>

                                            </ItemTemplate>
                                        </asp:TemplateField>
                                    </Columns>
                                </asp:GridView>
                            </div>
                        </div>
                    </div>
                </div>
            </asp:Panel>
        </ContentTemplate>
    </asp:UpdatePanel>
    <!-- التوجيه التلقائي -->
    <asp:UpdatePanel ID="UpdatePanel4" runat="server">
        <ContentTemplate>

<asp:Panel ID="pnlAutoRouting" runat="server" CssClass="tab-panel" Visible="false">
    <div class="row">
        <!-- إضافة مسار تلقائي جديد -->
        <div class="col-md-12">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 style="color: #007bff;">✨ إضافة مسار تلقائي جديد</h5>
                </div>
                <div class="card-body">
                                <asp:UpdatePanel ID="UpdatePanel3" runat="server">
                                    <ContentTemplate>
                                   <div class="row">
    <!-- نوع الطلب -->
    <div class="col-md-4 form-group">
        <label>نوع الطلب:</label>
        <asp:DropDownList ID="ddlAutoRequestType" runat="server" CssClass="form-control"></asp:DropDownList>
    </div>

    <!-- الوظيفة -->
    <div class="col-md-4 form-group">
        <label>الوظيفة:</label>
        <asp:DropDownList ID="ddlAutoRequestJob" runat="server" CssClass="form-control">
            <asp:ListItem Text="الكل" Value="ALL" />
        </asp:DropDownList>
    </div>

    <!-- الجنسية -->
    <div class="col-md-4 form-group">
        <label>الجنسية:</label>
        <asp:DropDownList ID="ddlAutoNationality" runat="server" CssClass="form-control">
            <asp:ListItem Text="" Value="" />
            <asp:ListItem Text="سعودي - Saudi" Value="سعودي - Saudi" />
            <asp:ListItem Text="غير سعودي - Non-Saudi" Value="غير سعودي - Non-Saudi" />
        </asp:DropDownList>
    </div>
</div>


                                        <!-- المشرفين -->
                                        <div class="form-group">
                                            <label>تحديد المشرفين:</label>
                                            <div class="supervisors-list">
                                                <asp:CheckBoxList ID="cblAutoSupervisors" runat="server"
                                                    CssClass="checkbox-list"
                                                    RepeatDirection="Vertical">
                                                </asp:CheckBoxList>
                                            </div>
                                        </div>

                                        <!-- ملاحظات -->
                                        <div class="form-group">
                                            <label>ملاحظات:</label>
                                            <asp:TextBox ID="txtAutoNotes" runat="server"
                                                TextMode="MultiLine"
                                                CssClass="form-control"
                                                Rows="3">
                                </asp:TextBox>
                                        </div>

                                        <!-- تفعيل المسار -->
                                        <div class="form-group">
                                            <asp:CheckBox ID="chkAutoActive" runat="server"
                                                Text="تفعيل المسار"
                                                Checked="true"
                                                CssClass="custom-checkbox" />
                                        </div>

                                        <!-- زر الحفظ -->
                                        <asp:Button ID="btnSaveAutoRoute" runat="server"
                                            Text="حفظ المسار"
                                            CssClass="btn btn-primary"
                                            OnClick="btnSaveAutoRoute_Click" />

                                        <asp:Label ID="LabelAutoMessage" runat="server" ForeColor="Green" Visible="false" />
                                        <asp:Label ID="LabelAutoError" runat="server" ForeColor="Red" Visible="false" />
                                    </ContentTemplate>
                                </asp:UpdatePanel>
                            </div>
                        </div>
                    </div>

                    <!-- عرض المسارات الحالية -->
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 style="color: #007bff;">📋 المسارات التلقائية النشطة</h5>
                                <!-- إضافة العداد هنا -->
                                <asp:Label ID="lblAutoActiveCount" runat="server"
                                    ForeColor="Green" >عدد المسارات النشطة</asp:Label>
                            </div>
                            <div class="card-body">
                                <asp:GridView ID="gvAutoRoutes" runat="server"
                                    CssClass="table table-bordered table-hover"
                                    AutoGenerateColumns="false"
                                    OnRowCommand="gvAutoRoutes_RowCommand1">
                                    <Columns>
                                        <asp:BoundField DataField="نوع_الطلب" HeaderText="نوع الطلب" />
                                        <asp:BoundField DataField="الجنسية" HeaderText="الجنسية" />
                                        <asp:BoundField DataField="الوظيفة" HeaderText="الوظيفة" />
                                        <asp:BoundField DataField="تاريخ_الإنشاء" HeaderText="تاريخ الإنشاء" DataFormatString="{0:dd/MM/yyyy}" />
                                        <asp:TemplateField HeaderText="حالة المسار">
                                            <ItemTemplate>
                                                <asp:Label runat="server"
                                                    CssClass='<%# Eval("حالة_المسار").ToString() == "True" ? "badge badge-success" : "badge badge-danger" %>'
                                                    Text='<%# Eval("حالة_المسار").ToString() == "True" ? "نشط" : "غير نشط" %>'>
                                    </asp:Label>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        <asp:TemplateField HeaderText="العمليات">
    <ItemTemplate>
        <asp:LinkButton ID="btnAutoEdit" runat="server"
            CommandName="EditRoute"
            CommandArgument='<%# Eval("ID") %>'
            CssClass="btn btn-sm btn-info">
            <i class="fas fa-edit"></i> تعديل
        </asp:LinkButton>

        <asp:LinkButton ID="btnAutoToggle" runat="server"
            CommandName="ToggleStatus"
            CommandArgument='<%# Eval("ID") %>'
            CssClass='<%# Eval("حالة_المسار").ToString() == "True" ? "btn btn-sm btn-warning" : "btn btn-sm btn-success" %>'>
            <i class="fas fa-power-off"></i> 
            <%# Eval("حالة_المسار").ToString() == "True" ? "تعطيل" : "تفعيل" %>
        </asp:LinkButton>

        <asp:LinkButton ID="btnAutoDelete" runat="server"
            CommandName="DeleteRoute"
            CommandArgument='<%# Eval("ID") %>'
            CssClass="btn btn-sm btn-danger"
            OnClientClick="return confirm('هل أنت متأكد من حذف هذا المسار؟');">
            <i class="fas fa-trash"></i> حذف
        </asp:LinkButton>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                    </Columns>
                                </asp:GridView>
                            </div>
                        </div>
                    </div>
                </div>

            </asp:Panel>

        </ContentTemplate>
    </asp:UpdatePanel>
    <!-- تنظيم جميع الألواح -->




    <!-- المسارات اليدوية -->
    <asp:Panel ID="pnlManualRouting" runat="server" CssClass="tab-panel" Visible="false">
        <div class="card mb-4">

            <div class="card-body">
                <div id="checkboxContainer" runat="server">
                    <div class="container">
                        <div class="row">
                            <!-- مسار 1 -->
                            <div class="col-md-4">
                                <div class="path-section path-1">
                                    <h4 class="path-title">مسار 1</h4>
                                    <div class="checkbox-list">
                                        <asp:CheckBox ID="CheckBox1" runat="server" Text="خدمات الموظفين"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox2" runat="server" Text="إدارة تخطيط الموارد البشرية"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox3" runat="server" Text="إدارة تقنية المعلومات"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox4" runat="server" Text="مراقبة الدوام"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox5" runat="server" Text="السجلات الطبية"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox6" runat="server" Text="إدارة الرواتب والاستحقاقات"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox7" runat="server" Text="إدارة القانونية والالتزام"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox8" runat="server" Text="خدمات الموارد البشرية"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox9" runat="server" Text="إدارة الإسكان"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox10" runat="server" Text="قسم الملفات"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox11" runat="server" Text="العيادات الخارجية"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox12" runat="server" Text="التأمينات الاجتماعية"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox13" runat="server" Text="وحدة مراقبة المخزون"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox14" runat="server" Text="إدارة تنمية الإيرادات"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox15" runat="server" Text="إدارة الأمن و السلامة"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox16" runat="server" Text="الطب الاتصالي"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                    </div>
                                </div>
                            </div>

                            <!-- مسار 2 -->
                            <div class="col-md-4">
                                <div class="path-section path-2">
                                    <h4 class="path-title">مسار 2</h4>
                                    <div class="checkbox-list">
                                        <asp:CheckBox ID="CheckBox17" runat="server" Text="خدمات الموظفين"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox18" runat="server" Text="إدارة تخطيط الموارد البشرية"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox19" runat="server" Text="إدارة تقنية المعلومات"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox20" runat="server" Text="مراقبة الدوام"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox21" runat="server" Text="السجلات الطبية"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox22" runat="server" Text="إدارة الرواتب والاستحقاقات"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox23" runat="server" Text="إدارة القانونية والالتزام"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox24" runat="server" Text="خدمات الموارد البشرية"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox25" runat="server" Text="إدارة الإسكان"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox26" runat="server" Text="قسم الملفات"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox27" runat="server" Text="العيادات الخارجية"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox28" runat="server" Text="التأمينات الاجتماعية"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox29" runat="server" Text="وحدة مراقبة المخزون"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox30" runat="server" Text="إدارة تنمية الإيرادات"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox31" runat="server" Text="إدارة الأمن و السلامة"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox32" runat="server" Text="الطب الاتصالي"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />

                                    </div>
                                </div>
                            </div>

                            <!-- مسار 3 -->
                            <div class="col-md-4">
                                <div class="path-section path-3">
                                    <h4 class="path-title">مسار 3</h4>
                                    <div class="checkbox-list">
                                        <asp:CheckBox ID="CheckBox33" runat="server" Text="خدمات الموظفين"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox34" runat="server" Text="إدارة تخطيط الموارد البشرية"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox35" runat="server" Text="إدارة تقنية المعلومات"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox36" runat="server" Text="مراقبة الدوام"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox37" runat="server" Text="السجلات الطبية"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox38" runat="server" Text="إدارة الرواتب والاستحقاقات"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox39" runat="server" Text="إدارة القانونية والالتزام"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox40" runat="server" Text="خدمات الموارد البشرية"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox41" runat="server" Text="إدارة الإسكان"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox42" runat="server" Text="قسم الملفات"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox43" runat="server" Text="العيادات الخارجية"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox44" runat="server" Text="التأمينات الاجتماعية"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox45" runat="server" Text="وحدة مراقبة المخزون"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox46" runat="server" Text="إدارة تنمية الإيرادات"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox47" runat="server" Text="إدارة الأمن و السلامة"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                        <asp:CheckBox ID="CheckBox48" runat="server" Text="الطب الاتصالي"
                                            AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
                                                </div>
    </div>
</div>
                            <!-- مسار 4 -->
<div class="col-md-4">
    <div class="path-section path-4">
        <h4 class="path-title">مسار 4</h4>
        <div class="checkbox-list">
            <asp:CheckBox ID="CheckBox49" runat="server" Text="خدمات الموظفين"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox50" runat="server" Text="إدارة تخطيط الموارد البشرية"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox51" runat="server" Text="إدارة تقنية المعلومات"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox52" runat="server" Text="مراقبة الدوام"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox53" runat="server" Text="السجلات الطبية"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox54" runat="server" Text="إدارة الرواتب والاستحقاقات"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox55" runat="server" Text="إدارة القانونية والالتزام"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox56" runat="server" Text="خدمات الموارد البشرية"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox57" runat="server" Text="إدارة الإسكان"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox58" runat="server" Text="قسم الملفات"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox59" runat="server" Text="العيادات الخارجية"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox60" runat="server" Text="التأمينات الاجتماعية"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox61" runat="server" Text="وحدة مراقبة المخزون"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox62" runat="server" Text="إدارة تنمية الإيرادات"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox63" runat="server" Text="إدارة الأمن و السلامة"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox64" runat="server" Text="الطب الاتصالي"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
        </div>
    </div>
</div>

<!-- مسار 5 -->
<div class="col-md-4">
    <div class="path-section path-5">
        <h4 class="path-title">مسار 5</h4>
        <div class="checkbox-list">
            <asp:CheckBox ID="CheckBox65" runat="server" Text="خدمات الموظفين"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox66" runat="server" Text="إدارة تخطيط الموارد البشرية"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox67" runat="server" Text="إدارة تقنية المعلومات"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox68" runat="server" Text="مراقبة الدوام"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox69" runat="server" Text="السجلات الطبية"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox70" runat="server" Text="إدارة الرواتب والاستحقاقات"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox71" runat="server" Text="إدارة القانونية والالتزام"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox72" runat="server" Text="خدمات الموارد البشرية"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox73" runat="server" Text="إدارة الإسكان"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox74" runat="server" Text="قسم الملفات"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox75" runat="server" Text="العيادات الخارجية"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox76" runat="server" Text="التأمينات الاجتماعية"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox77" runat="server" Text="وحدة مراقبة المخزون"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox78" runat="server" Text="إدارة تنمية الإيرادات"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox79" runat="server" Text="إدارة الأمن و السلامة"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox80" runat="server" Text="الطب الاتصالي"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
 </div>
        </div>
    </div>
            <!-- مسار 6 -->
<div class="col-md-4">
    <div class="path-section path-6">
        <h4 class="path-title">مسار 6</h4>
        <div class="checkbox-list">
            <asp:CheckBox ID="CheckBox81" runat="server" Text="خدمات الموظفين"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox82" runat="server" Text="إدارة تخطيط الموارد البشرية"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox83" runat="server" Text="إدارة تقنية المعلومات"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox84" runat="server" Text="مراقبة الدوام"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox85" runat="server" Text="السجلات الطبية"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox86" runat="server" Text="إدارة الرواتب والاستحقاقات"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox87" runat="server" Text="إدارة القانونية والالتزام"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox88" runat="server" Text="خدمات الموارد البشرية"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox89" runat="server" Text="إدارة الإسكان"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox90" runat="server" Text="قسم الملفات"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox91" runat="server" Text="العيادات الخارجية"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox92" runat="server" Text="التأمينات الاجتماعية"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox93" runat="server" Text="وحدة مراقبة المخزون"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox94" runat="server" Text="إدارة تنمية الإيرادات"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox95" runat="server" Text="إدارة الأمن و السلامة"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
            <asp:CheckBox ID="CheckBox96" runat="server" Text="الطب الاتصالي"
                AutoPostBack="true" OnCheckedChanged="CheckBox_CheckedChanged" />
       
                                    </div>
                                    <!-- نهاية checkbox-list -->
                                </div>
                                <!-- نهاية path-section -->
                            </div>
                            <!-- نهاية col-md-4 -->
                        </div>
                        <!-- نهاية row -->
                    </div>
                    <!-- نهاية container -->
                </div>
                <!-- نهاية checkboxContainer -->
            </div>
            <!-- نهاية card-body -->
        </div>
        <!-- نهاية card -->
    </asp:Panel>
    <!-- نهاية pnlManualRouting -->




    <style>
        /* تنسيق عام للمسارات */
        .path-section {
            background-color: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.05);
            height: 100%;
            min-height: 600px; /* تحديد ارتفاع ثابت */
            display: flex;
            flex-direction: column;
        }

        /* تنسيق العناوين */
        .path-title {
            text-align: center;
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 8px;
            font-weight: bold;
        }

        /* الألوان المميزة لكل مسار */
        .path-1 {
            border-top: 4px solid #0056b3;
        }

            .path-1 .path-title {
                background-color: #e3f2fd;
                color: #0056b3;
            }

        .path-2 {
            border-top: 4px solid #28a745;
        }

            .path-2 .path-title {
                background-color: #e8f5e9;
                color: #28a745;
            }

        .path-3 {
            border-top: 4px solid #dc3545;
        }

            .path-3 .path-title {
                background-color: #fbe9e7;
                color: #dc3545;
            }
            /* الألوان المميزة لكل مسار */
.path-1 {
    border-top: 4px solid #0056b3;
}

    .path-1 .path-title {
        background-color: #e3f2fd;
        color: #0056b3;
    }

.path-2 {
    border-top: 4px solid #28a745;
}

    .path-2 .path-title {
        background-color: #e8f5e9;
        color: #28a745;
    }

.path-3 {
    border-top: 4px solid #dc3545;
}

    .path-3 .path-title {
        background-color: #fbe9e7;
        color: #dc3545;
    }

/* مسار 4 */
.path-4 {
    border-top: 4px solid #ffc107;
}

    .path-4 .path-title {
        background-color: #fff8e1;
        color: #ffc107;
    }

/* مسار 5 */
.path-5 {
    border-top: 4px solid #17a2b8;
}

    .path-5 .path-title {
        background-color: #e0f7fa;
        color: #17a2b8;
    }

/* مسار 6 */
.path-6 {
    border-top: 4px solid #6f42c1;
}

    .path-6 .path-title {
        background-color: #f3e5f5;
        color: #6f42c1;
    }


        /* تنسيق قائمة الاختيارات */
        .checkbox-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
            overflow-y: auto;
            padding: 10px;
        }

            .checkbox-list label {
                display: block;
                padding: 10px 15px;
                margin: 0;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                background-color: #f8f9fa;
                transition: all 0.2s ease;
                cursor: pointer;
                font-size: 14px;
            }

                .checkbox-list label:hover {
                    background-color: #e9ecef;
                    transform: translateX(-5px);
                }

            .checkbox-list input[type="checkbox"] {
                margin-left: 10px;
                transform: scale(1.2);
            }

            /* تنسيق الحالة المحددة */
            .checkbox-list label.checked {
                background-color: #e3f2fd;
                border-color: #0056b3;
            }

            /* تنسيق شريط التمرير */
            .checkbox-list::-webkit-scrollbar {
                width: 6px;
            }

            .checkbox-list::-webkit-scrollbar-track {
                background: #f1f1f1;
                border-radius: 3px;
            }

            .checkbox-list::-webkit-scrollbar-thumb {
                background: #888;
                border-radius: 3px;
            }

                .checkbox-list::-webkit-scrollbar-thumb:hover {
                    background: #555;
                }



        /* تنسيق البطاقة الرئيسية */
        .card {
            background-color: white;
            border: none;
            border-radius: 12px;
            box-shadow: 0 2px 15px rgba(0,0,0,0.08);
            margin-top: 25px;
        }

        .card-header {
            background: linear-gradient(145deg, #0056b3, #007bff);
            color: white;
            padding: 20px;
            border-radius: 12px 12px 0 0;
            border: none;
        }

            .card-header h3 {
                margin: 0;
                font-weight: 600;
            }

        .card-body {
            padding: 25px;
        }

        /* تنسيق القوائم المنسدلة */
        .form-control {
            height: 45px;
            border-radius: 8px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
            padding: 8px 15px;
            font-size: 14px;
        }

            .form-control:focus {
                border-color: #0056b3;
                box-shadow: 0 0 0 3px rgba(0,86,179,0.1);
            }

        label {
            font-weight: 500;
            color: #2c3e50;
            margin-bottom: 8px;
            display: block;
        }

        /* تنسيق قسم المشرفين */
        .supervisors-section {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 25px;
        }

            .supervisors-section h4 {
                color: #0056b3;
                font-weight: 600;
                padding-bottom: 15px;
                border-bottom: 2px solid #e9ecef;
                margin-bottom: 20px;
            }

        .supervisors-grid {
            display: grid;
            gap: 15px;
            padding: 10px;
        }

        .supervisors-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 12px;
        }

            .supervisors-list label {
                background-color: white;
                padding: 12px 15px;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                margin: 0;
                transition: all 0.3s ease;
                cursor: pointer;
                display: flex;
                align-items: center;
            }

                .supervisors-list label:hover {
                    background-color: #f8f9fa;
                    transform: translateX(-5px);
                    border-color: #0056b3;
                }

            .supervisors-list input[type="checkbox"] {
                margin-left: 10px;
                transform: scale(1.2);
            }

        /* تنسيق الأزرار */
        .actions-section {
            margin-top: 30px;
            display: flex;
            gap: 15px;
        }

        .btn {
            padding: 10px 25px;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(145deg, #0056b3, #007bff);
            border: none;
        }

            .btn-primary:hover {
                background: linear-gradient(145deg, #004494, #0056b3);
                transform: translateY(-2px);
            }

        .btn-secondary {
            background-color: #6c757d;
            border: none;
        }

            .btn-secondary:hover {
                background-color: #5a6268;
                transform: translateY(-2px);
            }

        /* تحسينات إضافية */
        .mb-4 {
            margin-bottom: 1.5rem;
        }

        .col-md-4 {
            margin-bottom: 15px;
        }

        /* تنسيق للشاشات الصغيرة */
        @media (max-width: 768px) {
            .card-body {
                padding: 15px;
            }

            .supervisors-list {
                grid-template-columns: 1fr;
            }
        }

        .tab-panel {
            padding: 20px;
            border: 1px solid #dee2e6;
            border-top: none;
            border-radius: 0 0 5px 5px;
        }

        .tab-panel {
            padding: 20px;
            border: 1px solid #dee2e6;
            border-top: none;
            border-radius: 0 0 5px 5px;
        }

        .routing-tabs {
            margin-bottom: 0; /* تعديل الهامش */
        }

        .tab-btn {
            padding: 10px 20px;
            margin-right: 5px;
            border: 1px solid #dee2e6;
            background-color: white;
            border-bottom: none;
            border-radius: 5px 5px 0 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

            .tab-btn:hover {
                background-color: #f8f9fa;
            }

            .tab-btn.active {
                background-color: #007bff;
                color: white;
                border-color: #007bff;
            }

        .routing-panel {
            padding: 20px;
        }

        .card {
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .card-header {
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            padding: 15px;
        }

        .supervisors-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
        }

        .custom-checkbox {
            margin-top: 10px;
        }

        .badge {
            padding: 8px 12px;
            border-radius: 4px;
        }

        .badge-success {
            background: #28a745;
        }

        .badge-danger {
            background: #dc3545;
        }
        .custom-upload-button {
    display: inline-block;
    padding: 8px 16px;
    background-color: #4CAF50;
    color: white;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 10px;
}

.upload-status-text {
    margin: 0 10px;
    color: #6c757d;
}

.button-group {
    margin: 20px 0;
    display: flex;
    align-items: center;
}


/* زر حذف جميع البيانات */
.danger-button {
    background-color: #b22222; /* أحمر */
    color: white;
    border: none;
    padding: 10px 20px;
    font-size: 16px;
    cursor: pointer;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}


.danger-button:hover {
    background-color: #800000; /* أحمر داكن عند التمرير */
}

/* زر تحديث الإحصائيات برتقالي */
.action-button {
    background-color: #fd7e14; /* برتقالي */
    color: white;
    border: none;
    padding: 10px 20px;
    font-size: 16px;
    cursor: pointer;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

.action-button:hover {
    background-color: #e36c0a; /* برتقالي داكن عند التمرير */
}


    </style>

    <!-- إضافة JavaScript للتأثيرات -->
    <script type="text/javascript">
        document.addEventListener('DOMContentLoaded', function () {
            // إضافة تأثير عند الاختيار
            var checkboxes = document.querySelectorAll('.checkbox-list input[type="checkbox"]');
            checkboxes.forEach(function (checkbox) {
                checkbox.addEventListener('change', function () {
                    var label = this.parentElement;
                    if (this.checked) {
                        label.classList.add('checked');
                    } else {
                        label.classList.remove('checked');
                    }
                });
            });
        });

         type="text/javascript">
            document.addEventListener('DOMContentLoaded', function () {
                var uploadButtons = document.getElementsByClassName('custom-upload-button');
                for (var i = 0; i < uploadButtons.length; i++) {
                    uploadButtons[i].addEventListener('click', function () {
                        var fileInput = this.querySelector('input[type="file"]');
                        if (fileInput) {
                            fileInput.click();
                        }
                    });
                }
            });

        function updateFileName() {
            var fileInput = document.getElementById('<%= fileUploadEmployees.ClientID %>');
            var fileName = document.getElementById('fileName');
            if (fileInput && fileInput.files.length > 0) {
                fileName.textContent = fileInput.files[0].name;
                fileName.style.color = '#28a745';

                // عرض التذكير للمستخدم
                alert('يرجى التأكد من أن الملف محفوظ بتنسيق CSV UTF-8 (مقسوم بالفاصلة) لضمان قراءة البيانات بشكل صحيح.');
            } else {
                fileName.textContent = 'لم يتم اختيار ملف';
                fileName.style.color = '#dc3545';
            }
        }

        function confirmDelete() {
            Swal.fire({
                title: 'هل أنت متأكد؟',
                text: 'سيتم حذف جميع البيانات!',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'نعم، احذفها!',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    // استدعاء زر الحذف من جانب الخادم
                    __doPostBack('<%= btnDeleteEmployees.UniqueID %>', '');
            }
        });
        }
        function updateEmployeeCount() {
            // استدعاء كود C# لتحديث العدد باستخدام Ajax
            __doPostBack('UpdateEmployeeCount', '');
        }



    </script>


   

</asp:Content>
