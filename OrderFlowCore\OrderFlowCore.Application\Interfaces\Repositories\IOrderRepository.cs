using OrderFlowCore.Core.Entities;
using OrderFlowCore.Core.Models;
using OrderFlowCore.Application.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Interfaces.Repositories;

public interface IOrderRepository
{
    Task<int> AddAsync(OrdersTable order);
    Task<int> UpdateAsync(OrdersTable order);
    Task<OrdersTable> GetByIdAsync(int id);
    Task<List<OrdersTable>> GetAllAsync();
    Task<List<OrdersTable>> GetPendingOrdersForDirectMangerAsync();
    Task<List<OrdersTable>> GetAssistantManagerOrdersAsync(AssistantManagerType assistantManagerId);
    Task<OrdersTable?> GetOrderByIdAsync(int orderId);
    Task<List<OrdersTable>> GetOrdersByStatusesAsync(string[] statuses);

    // HR Coordinator specific methods
    Task<List<OrdersTable>> GetHRCoordinatorPendingOrders();
    Task<List<SupervisorRejectionDto>> GetSupervisorRejectionsAsync(int orderId);
    Task UpdateSupervisorStatusesAsync(int orderId, List<string> selectedSupervisors, string statusWithDate);
    Task ClearSupervisorStatusesAsync(int orderId);
    Task<List<RestorableOrderDto>> GetRestorableOrdersAsync(string searchTerm, string filter);
    Task<RestoreDetailsDto> GetRestoreOrderDetailsAsync(int orderId);
    Task UpdateOrderStatusesAsync();
}
